# ProductDiscoveryService Implementation

## Overview

The ProductDiscoveryService is a service that orchestrates product discovery by extracting search queries from user messages and executing searches using SearchService. It works in conjunction with SearchQueryExtractor (AI-powered) to provide natural language product search capabilities.

## Architecture

The implementation follows a clean separation of concerns:

```
User Query → IntentRouter → OrchestrationService → ProductDiscoveryService
                                                          ↓
                                              SearchQueryExtractor + SearchService
```

## Components

### 1. SearchQueryExtractor Interface
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/service/SearchQueryExtractor.java`
- **Purpose**: AI service interface with LangChain4j annotations for query extraction
- **Features**:
  - Extracts up to 5 search terms from user messages
  - Returns List<String> for use with SearchService.searchQueries()
  - Uses structured JSON output format

### 2. ProductDiscoveryService Class
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/service/ProductDiscoveryService.java`
- **Purpose**: Orchestrates query extraction and search execution
- **Methods**:
  - `discoverProducts(String userMessage)` - Main method that extracts queries and executes search
  - Returns `SearchResult` containing extracted queries and search results

### 3. OrchestrationService Class
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/service/OrchestrationService.java`
- **Purpose**: Main conversation orchestrator implementing ConversationService
- **Features**:
  - Routes user intents using IntentRouter
  - Handles product discovery flow
  - Creates structured API responses (ConverseResponse, SuggestResponse)

### 4. ServiceFactory Configuration
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/service/ServiceFactory.java`
- **Purpose**: Bean factory for AI services
- **Configuration**:
  - Creates SearchQueryExtractor bean with model configuration
  - Model provider support (OpenAI, Google Gemini)
  - No memory management needed for query extraction

## Configuration

### Model Configuration (application.yaml)
```yaml
llm:
  models:
    product-discovery:
      provider: GOOGLE_AI_GEMINI
      api-key: ${gemini.api-key}
      model-name: gemini-2.5-flash
      temperature: 0.5
      log-requests: true
      log-responses: true
```

### Search Configuration
```yaml
retail:
  search:
    collection: zapparel
    area: Production
    page-size: 20
    product-fields:
      - productId
      - title
      - description
      - price
      - originalPrice
```

## Usage Examples

### Product Discovery Flow
```java
@Inject
ProductDiscoveryService productDiscoveryService;

ProductDiscoveryService.SearchResult result = productDiscoveryService.discoverProducts(
    "I'm looking for a warm jacket for winter"
);

// Access extracted queries
List<String> queries = result.getExtractedQueries();
// Access search results
Map<String, Map<String, Object>> searchResults = result.getSearchResults();
```

### Complete API Flow
```java
@Inject
OrchestrationService orchestrationService;

// For suggestions
SuggestRequest suggestRequest = new SuggestRequest("visitor-123", "session-456", "winter jacket");
SuggestResponse suggestions = orchestrationService.suggest(suggestRequest);

// For conversation
ConverseRequest converseRequest = new ConverseRequest(
    "visitor-123",
    null,
    "session-456",
    List.of(new Message(MessageType.TEXT, "I need a warm jacket")),
    null,
    false
);
ConverseResponse response = orchestrationService.converse(converseRequest);
```

## Integration with Existing Services

### SearchService Integration
The ProductDiscoveryService uses SearchService through ProductDiscoveryTools:
- Converts natural language queries to search terms
- Handles multiple search variations
- Processes search results for conversational presentation

### Memory Management
- Maintains conversation context across multiple turns
- Uses MessageWindowChatMemory with 20 message limit
- Session-based memory isolation

### Error Handling
- Graceful degradation when search fails
- Logging for debugging and monitoring
- User-friendly error messages

## Testing

### Unit Tests
- **Location**: `src/test/java/com/groupbyinc/ca/application/convo/ai/service/ProductDiscoveryServiceTest.java`
- **Coverage**: Component injection, prompt loading, tool functionality

### Integration Tests
- Requires LLM API keys for full testing
- Use `INTEGRATION_TEST=true` environment variable
- Tests end-to-end conversation flows

## Key Features

### 1. Natural Language Understanding
- Processes ambiguous product queries
- Understands context and intent
- Handles follow-up questions

### 2. Intelligent Search Strategy
- Multiple search term generation
- Synonym and variation handling
- Result relevance optimization

### 3. Personalization
- User context integration
- Purchase history consideration
- Preference-based recommendations

### 4. Conversational Flow
- Memory-based context retention
- Natural dialogue progression
- Clarifying question generation

## Future Enhancements

1. **Semantic Search Integration**: Add vector database support for contextual search
2. **Cart Management**: Integrate cart operations as additional tools
3. **Product Comparison**: Add tools for comparing multiple products
4. **Image Analysis**: Support image-based product queries
5. **Recommendation Engine**: Integrate collaborative filtering recommendations

## Troubleshooting

### Common Issues

1. **Prompt Template Not Found**
   - Ensure `/prompts/product-discovery.md` exists in resources
   - Check file path in ServiceFactory configuration

2. **Model Configuration Issues**
   - Verify API keys are properly set
   - Check model provider configuration in application.yaml

3. **Search Integration Problems**
   - Verify SearchService is properly configured
   - Check retail search configuration parameters

4. **Memory Issues**
   - Monitor conversation length
   - Adjust MessageWindowChatMemory size if needed

### Logging
Enable debug logging for detailed troubleshooting:
```yaml
logger:
  levels:
    com.groupbyinc.ca.application.convo.ai: DEBUG
```

## Dependencies

- LangChain4j 1.1+
- Micronaut 4.5+
- Jackson (for JSON serialization)
- Existing SearchService and SearchClient
