# ProductDiscoveryService Implementation

## Overview

The ProductDiscoveryService is an AI-powered service that acts as an intelligent shopping assistant for product discovery and recommendations. It integrates with the existing SearchService to provide natural language product search capabilities using LangChain4j framework.

## Architecture

The implementation follows the **Router -> Tool-Using Agent** pattern described in the LangChain4j integration documentation:

```
User Query → IntentRouter → ProductDiscoveryService → SearchService
                                     ↓
                              ProductDiscoveryTools
```

## Components

### 1. ProductDiscoveryService Interface
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/service/ProductDiscoveryService.java`
- **Purpose**: AI service interface with LangChain4j annotations
- **Features**:
  - Memory management with `@MemoryId`
  - User context injection with `@V("user_context")`
  - Natural language processing with `@UserMessage`

### 2. ProductDiscoveryTools Class
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/tools/ProductDiscoveryTools.java`
- **Purpose**: Wrapper for SearchService with `@Tool` annotations
- **Methods**:
  - `searchProducts(List<String> queries)` - Multi-query search
  - `searchProduct(String query)` - Single query search
  - `searchProductVariations(String primary, List<String> alternatives)` - Variation search

### 3. Prompt Template
- **Location**: `src/main/resources/prompts/product-discovery.md`
- **Purpose**: Externalized prompt for AI behavior configuration
- **Features**:
  - Conversational guidelines
  - Search strategy instructions
  - User context integration
  - Response formatting rules

### 4. ServiceFactory Configuration
- **Location**: `src/main/java/com/groupbyinc/ca/application/convo/ai/service/ServiceFactory.java`
- **Purpose**: Bean factory for AI services
- **Configuration**:
  - Model provider support (OpenAI, Google Gemini)
  - Memory management (20 message window)
  - Tool integration
  - Prompt template loading

## Configuration

### Model Configuration (application.yaml)
```yaml
llm:
  models:
    product-discovery:
      provider: GOOGLE_AI_GEMINI
      api-key: ${gemini.api-key}
      model-name: gemini-2.5-flash
      temperature: 0.5
      log-requests: true
      log-responses: true
```

### Search Configuration
```yaml
retail:
  search:
    collection: zapparel
    area: Production
    page-size: 20
    product-fields:
      - productId
      - title
      - description
      - price
      - originalPrice
```

## Usage Examples

### Simple Product Discovery
```java
@Inject
ProductDiscoveryService productDiscoveryService;

String response = productDiscoveryService.chat(
    "session-123", 
    "I'm looking for a warm jacket for winter"
);
```

### Personalized Discovery with User Context
```java
String userContext = """
{
  "name": "John",
  "preferences": ["outdoor gear", "sustainable materials"],
  "purchaseHistory": [
    {"productName": "Hiking Boots", "category": "footwear"}
  ]
}
""";

String response = productDiscoveryService.chat(
    "session-123",
    "I need something for my next hiking trip",
    userContext
);
```

## Integration with Existing Services

### SearchService Integration
The ProductDiscoveryService uses SearchService through ProductDiscoveryTools:
- Converts natural language queries to search terms
- Handles multiple search variations
- Processes search results for conversational presentation

### Memory Management
- Maintains conversation context across multiple turns
- Uses MessageWindowChatMemory with 20 message limit
- Session-based memory isolation

### Error Handling
- Graceful degradation when search fails
- Logging for debugging and monitoring
- User-friendly error messages

## Testing

### Unit Tests
- **Location**: `src/test/java/com/groupbyinc/ca/application/convo/ai/service/ProductDiscoveryServiceTest.java`
- **Coverage**: Component injection, prompt loading, tool functionality

### Integration Tests
- Requires LLM API keys for full testing
- Use `INTEGRATION_TEST=true` environment variable
- Tests end-to-end conversation flows

## Key Features

### 1. Natural Language Understanding
- Processes ambiguous product queries
- Understands context and intent
- Handles follow-up questions

### 2. Intelligent Search Strategy
- Multiple search term generation
- Synonym and variation handling
- Result relevance optimization

### 3. Personalization
- User context integration
- Purchase history consideration
- Preference-based recommendations

### 4. Conversational Flow
- Memory-based context retention
- Natural dialogue progression
- Clarifying question generation

## Future Enhancements

1. **Semantic Search Integration**: Add vector database support for contextual search
2. **Cart Management**: Integrate cart operations as additional tools
3. **Product Comparison**: Add tools for comparing multiple products
4. **Image Analysis**: Support image-based product queries
5. **Recommendation Engine**: Integrate collaborative filtering recommendations

## Troubleshooting

### Common Issues

1. **Prompt Template Not Found**
   - Ensure `/prompts/product-discovery.md` exists in resources
   - Check file path in ServiceFactory configuration

2. **Model Configuration Issues**
   - Verify API keys are properly set
   - Check model provider configuration in application.yaml

3. **Search Integration Problems**
   - Verify SearchService is properly configured
   - Check retail search configuration parameters

4. **Memory Issues**
   - Monitor conversation length
   - Adjust MessageWindowChatMemory size if needed

### Logging
Enable debug logging for detailed troubleshooting:
```yaml
logger:
  levels:
    com.groupbyinc.ca.application.convo.ai: DEBUG
```

## Dependencies

- LangChain4j 1.1+
- Micronaut 4.5+
- Jackson (for JSON serialization)
- Existing SearchService and SearchClient
