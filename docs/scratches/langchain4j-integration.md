# LangChain4j Integration: An Architectural Guide

This document outlines an architectural approach for integrating LangChain4j into a Micronaut-based Conversational Commerce Engine. The patterns described are based on the official LangChain4j documentation and are designed to produce a scalable and maintainable system using Java 21, Micronaut 4.5+, and LangChain4j 1.1+.

The code examples provided here are conceptual, serve as a starting point, and should be adapted to fit the specific requirements of the application.

## 1. Conversational Workflow & Structured Output

The primary architectural pattern is Router -> Tool-Using Agent. This involves a routing component that first determines user intent and then delegates the request to a specialized, tool-equipped agent. This approach promotes separation of concerns and modularity.

### LangChain4j Implementation Strategy

#### Step 1: Define a Router with a Structured Return Type

An AiService is defined to act as the router. It leverages LangChain4j's structured output feature by returning a Java record or POJO. This instructs the LLM to respond with a structured JSON object, which is automatically deserialized into the specified class.

```java
// In: com.groupbyinc.ca.application.convo.ai.router
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import java.util.List;

public interface IntentRouter {

    // Define the structured output as a Java Record
    record RouteInstruction(Route route, List<String> extractedEntities) {}

    enum Route {
        PRODUCT_DISCOVERY,
        RETURN_REFUND,
        CUSTOMER_SERVICE,
        CART_MANAGEMENT,
        UNKNOWN
    }

    @SystemMessage("""
        You are a master router for a conversational commerce AI.
        Based on the user's message, identify the primary intent and any key entities like product names or brands.
        Respond with a JSON object matching the required format.
        """)
    RouteInstruction route(@UserMessage String userMessage, @dev.langchain4j.service.MemoryId String sessionId);
}
```

#### Step 2: Define Specialist Agents

Specialist agents are defined as separate AiService interfaces. Each is equipped with a specific set of tools (annotated with @Tool) to perform actions like searching a product catalog or managing a shopping cart.

#### Step 3: Orchestrate the Flow

A central orchestrator service (e.g., a Micronaut @Singleton) manages the workflow. It first calls the router to determine intent and then invokes the appropriate specialist agent based on the router's response.

```java
// In: com.groupbyinc.ca.application.convo.OrchestrationService
import jakarta.inject.Singleton;
// ... other imports

@Singleton
public class OrchestrationService {

    private final IntentRouter router;
    private final ProductDiscoveryAgent productDiscoveryAgent;
    // ... other agents

    public OrchestrationService(IntentRouter router, ProductDiscoveryAgent productDiscoveryAgent, /*...*/) {
        this.router = router;
        this.productDiscoveryAgent = productDiscoveryAgent;
    }

    public String handleRequest(String sessionId, String userMessage, /*...*/) {
        // 1. Route the request to get a structured instruction
        IntentRouter.RouteInstruction instruction = router.route(userMessage, sessionId);

        // 2. Delegate to the appropriate specialist agent
        return switch (instruction.route()) {
            case PRODUCT_DISCOVERY -> productDiscoveryAgent.chat(sessionId, userMessage, /*...*/);
            // case CART_MANAGEMENT -> cartAgent.chat(...);
            // case CUSTOMER_SERVICE -> customerServiceAgent.chat(...);
            default -> "I'm sorry, I'm not sure how to help with that. Could you please rephrase?";
        };
    }
}
```

## 2. Externalized Prompt Management
For production systems, prompts should be externalized from the application code. A recommended strategy is to store prompt templates in external files (e.g., Markdown) and load them at runtime.

### File Structure:

```
src/main/resources/
└── prompts/
    ├── router.md
    └── product-discovery.md
```

### PromptManager Implementation:

A singleton service can be implemented to handle the loading and creation of PromptTemplate objects from the resource files.

```java
// In: com.groupbyinc.ca.application.convo.ai.PromptManager
import dev.langchain4j.model.input.PromptTemplate;
import jakarta.inject.Singleton;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.nio.charset.StandardCharsets;

@Singleton
public class PromptManager {
    public PromptTemplate getPromptTemplate(String name) {
        String path = "/prompts/" + name + ".md";
        try (var inputStream = PromptManager.class.getResourceAsStream(path)) {
            if (inputStream == null) {
                throw new IllegalArgumentException("Prompt file not found: " + path);
            }
            String templateString = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            return PromptTemplate.from(templateString);
        } catch (IOException e) {
            throw new UncheckedIOException("Failed to load prompt: " + name, e);
        }
    }
}
```

### AiService Factory:

The AiServices are then built programmatically in a Micronaut @Factory class, using the PromptManager to supply the system prompt.

```java
// In a Micronaut Factory class (e.g., AiClientFactory.java)
import io.micronaut.context.annotation.Factory;
// ... other imports

@Factory
public class AiServiceFactory {
    @Singleton
    public ProductDiscoveryAgent productDiscoveryAgent(
            ChatLanguageModel chatLanguageModel,
            ChatMemoryProvider memoryProvider,
            ProductDiscoveryTools tools,
            PromptManager promptManager
    ) {
        PromptTemplate systemPromptTemplate = promptManager.getPromptTemplate("product-discovery");

        return AiServices.builder(ProductDiscoveryAgent.class)
                .chatLanguageModel(chatLanguageModel)
                .chatMemoryProvider(memoryProvider)
                .tools(tools)
                .systemMessageProvider(systemPromptTemplate::apply)
                .build();
    }
}
```

## 3. Memory Management: Separating Chat History and User Context
A clear distinction must be made between ephemeral conversation history and persistent user state.

### A. Persistent Chat History

The `dev.langchain4j.store.memory.chat.ChatMemoryStore` interface should be implemented to persist the sequence of ChatMessage objects to a database like MongoDB. This implementation is then wrapped in a ChatMemoryProvider and supplied to all AiServices, ensuring they share the same conversation history for a given session ID.

### B. Handling Custom User Context

Stateful data not part of the direct conversation, such as a user's profile or purchase history (UserContext), should be managed separately. Passing a raw Java object directly to an AiService is an anti-pattern, as it will be converted to a string using its toString() method, which is not useful to an LLM.

The correct pattern is to serialize the context object into a structured string format (e.g., JSON) and pass it to the agent as a dedicated parameter using the @V annotation.

#### Step 1: Serialize User Context in the Orchestrator

The orchestrator fetches the UserContext and serializes it to a JSON string.

```java
// In: com.groupbyinc.ca.application.convo.OrchestrationService
// ...
private final UserContextRepository userContextRepository;
private final ObjectMapper objectMapper; // For serializing context to JSON

// ... constructor injection

public String handleRequest(String sessionId, String userId, String userMessage) {
    // 1. Fetch the user's context
    UserContext userContext = userContextRepository.findById(userId).orElseGet(() -> new UserContext(userId));
    String userContextJson = "{}";
    try {
        userContextJson = objectMapper.writeValueAsString(userContext);
    } catch (JsonProcessingException e) {
        // log error, proceed with empty context
    }

    // 2. Delegate to the agent, passing the serialized context
    return productDiscoveryAgent.chat(sessionId, userMessage, userContextJson);
}
```

#### Step 2: Update AiService to Accept the Serialized Context

The AiService interface is modified to accept the JSON string as a parameter annotated with @V. This annotation links the parameter to a variable in the prompt template.

```java
// In: com.groupbyinc.ca.application.convo.ai.product.ProductDiscoveryAgent
public interface ProductDiscoveryAgent {
    String chat(
        @MemoryId String sessionId,
        @UserMessage String userMessage,
        @V("user_context") String userContextJson
    );
}
```

#### Step 3: Update Prompt to Use the Context

The prompt file can now reference the `{{user_context}}` variable, which will be populated with the JSON string.

**product-discovery.md:**

```markdown
You are an expert sales agent. Use available tools to answer the user's questions.
Personalize your recommendations using the following user context.
<user_context>
{{user_context}}
</user_context>
```

This architecture correctly separates concerns: the ChatMemoryStore handles dialogue flow, while the UserContext provides rich, stateful data for personalization, injected into the prompt in a structured, machine-readable format.