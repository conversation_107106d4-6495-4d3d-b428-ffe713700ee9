# LLM Prompt Analysis from JS Prototype

## 1. Overview

This document analyzes the LLM prompts used in the legacy Node.js prototype. The prototype employed a clever, albeit outdated, method of generating structured data by instructing the LLM to use specific delimiters (`:`, `|`, `~`) in its string responses. The front-end application would then parse these strings to render the UI.

While our new Java-based orchestrator will use native JSON mode for more reliable and robust structured output, the logic embedded within these prompts offers valuable insights into the core conversational workflows. We can adapt the intent of these prompts into our more modern architecture.

## 2. Prompt Analysis

### Prompt 1: Predictive Search-as-You-Type (`/ai-sayt`)

This endpoint generates predictive search queries as the user types.

#### Prompt Content
```
Using the text submitted by the user, please predict the question they are asking as it pertains to an online housewares store search. Respond with 5 likely questions that fit, with no other text and no bullets or numbering, and separate each question with a ~
```

#### Purpose & Use Case

**Trigger:** Fired on each keystroke in the search input field.

**Function:** Takes a partial user query (e.g., "red kitch") and asks the LLM to autocomplete it into five plausible, full-sentence questions related to a housewares store.

**Output Format:** A single string with five questions separated by a `~` delimiter (e.g., `red kitchenaid mixer~red kitchen towels~are there any sales on red kitchen items~...`).

**Frontend Action:** The backend splits the string by the delimiter and displays the five questions as clickable suggestions.

#### Architectural Comments & Project Utilization

**Assessment:** This is a solid feature for enhancing user experience (FR14). The core idea of using an LLM for predictive, conversational search suggestions is powerful.

**Our Implementation:** We will replicate this functionality. However, instead of relying on a delimited string, our Java orchestrator will request a JSON object from the LLM.

**Proposed JSON Schema:**

```json
{
  "suggestions": [
    "string",
    "string",
    "string",
    "string",
    "string"
  ]
}
```

This approach eliminates parsing errors and provides a clean, predictable data structure for the UI.

### Prompt 2: Image-Based Contextual Search (`/image-context`)

This endpoint analyzes a user-uploaded image alongside their text query to provide more relevant search terms.

#### Prompt Content
```
Please respond with just a ${req.body.num}, followed by a colon, followed by a comma-delimited list of search terms that are better for finding what the user is looking for based on their request and the image provided - if the original request asked for sale items or stuff on sale, then add " on sale" to each search term - then after all of that place a colon, followed by a friendly message about the types of things being suggested and why they are good matches, followed by a | character, followed by a similar but more succinct message
```

(Note: `${req.body.num}` is a variable, typically 1 or 2, used for internal state tracking.)

#### Purpose & Use Case

**Trigger:** User uploads an image and provides a text query (e.g., "find me pillows that match this couch").

**Function:** The LLM analyzes both the image and the text to generate relevant search terms, a friendly explanation, and a shorter summary. It also handles conditional logic, like adding "on sale" if requested.

**Output Format:** A complex delimited string, e.g., `1:throw pillows,decorative cushions:These pillows would complement the color of your couch|Matching pillows for your couch`.

**Frontend Action:** The frontend parses this string to display the suggested search terms and the AI's commentary.

#### Architectural Comments & Project Utilization

**Assessment:** This directly supports our multi-modal requirement (FR9). The logic of combining visual and text context is a key differentiator. The prompt's logic for appending "on sale" is a good example of the business rules our orchestrator will manage.

**Our Implementation:** We will implement this as a core feature. The orchestrator will receive the image and text, pass them to a multi-modal LLM, and request a structured JSON response.

**Proposed JSON Schema:**

```json
{
  "responseCode": "integer",
  "searchTerms": ["string"],
  "verboseMessage": "string",
  "shortMessage": "string"
}
```

This eliminates the complex and error-prone string parsing and provides a clear contract for the UI.

### Prompt 3: Main Conversation Handler (`/ai`)

This is the primary endpoint for handling the main conversational flow. It's a large, complex prompt that attempts to create a state machine and response formatter using pure text instructions.

#### Prompt Content
```
Please analyze the user's request. For any part that responds with search terms, include phrases like "murphy beds" if the user is looking to organize a bedroom or free up space in a bachelor apartment. If the user is asking for information about shipping or the store's return policy, respond with just a 0 followed by a colon, then followed by a friendly message... [and so on, with extensive rules for different scenarios] ... If it is asking for a list of items that match certain criteria... then respond with just a 1. If the user is asking help to find a specific product... then respond with just a 2. ... If they were not asking for anything to go well with something else, and if the response was just a 1, then place a colon after the 1 and follow that with just a comma-delimited list of search terms... After that comma-delimited list, place another colon, and following that, provide an answer to the user... Please follow this with a | character, followed by a similar but much more succinct/brief message. If you have asked the user to share a photo for context, then add another colon to the response, followed by the word: photo.
```

#### Purpose & Use Case

**Trigger:** Any general user message in the chat interface.

**Function:** This is the workhorse prompt. It tries to do everything:

- **Intent Recognition:** Determines if the user is asking for products, information (shipping/returns), or making an ambiguous statement.
- **State Management:** Uses numeric codes (0, 1, 2) to classify the type of response needed.
- **Search Term Generation:** Extracts and enhances search terms from the user's query.
- **Response Generation:** Crafts both a verbose and a short message for the user.
- **Special Flag Handling:** Appends flags like `:photo` or `~newArrivals` to trigger specific UI actions.

**Output Format:** A highly structured, colon-delimited string that the frontend must carefully parse.

#### Architectural Comments & Project Utilization

**Assessment:** This prompt is a masterclass in "prompt-as-architecture." It's impressive but highlights exactly why a dedicated orchestrator is necessary. All the logic (routing, state management, conditional responses) is brittlely encoded in the prompt. A small change in the LLM's output format could break the entire application.

**Our Implementation:** We will deconstruct this monolithic prompt and implement its logic within our Java orchestrator.

- **Intent Recognition:** The orchestrator will make an initial LLM call to classify user intent (e.g., `product_query`, `info_query`, `chitchat`). This aligns with the "Routing" workflow from the "Building effective agents" document.
- **State Management:** The orchestrator will manage the conversation state, not the LLM.
- **Tool Use:** Instead of asking the LLM to generate search terms in a string, we will provide it with `retail_search` and `semantic_search` tools. The LLM will decide which tool to call and with what parameters.
- **Response Generation:** The final user-facing message will be generated by the LLM based on the results from the tool calls, with the orchestrator requesting a clean JSON object.

### Prompt 4: Structured JSON Response (`/ai-json`)

This endpoint was an experimental, unused version that attempted to use the LLM's native JSON mode with a predefined schema.

#### Prompt Content

(The core of this prompt is the extensive list of product categories and the JSON schema definition)

```
Please analyze the user's request. For any part that responds with search terms, include phrases like "murphy beds"... First, determine if the request has anything to do with this online store by checking if the user's request is asking about any products that might be found in this list of main categories: ["Party Supplies","Garden Decor", ... 300+ more categories] ... If the query has nothing to do with any of those main categories... then set response_code to 0...
```

#### JSON Schema Definition in Prompt
```json
"response_format": {
  "type": "json_schema",
  "json_schema": {
    "name": "query_response",
    "description": "schema for results after analyzing user query",
    "strict": true,
    "schema": {
      "type": "object",
      "properties": {
        "response_code": { "type": "integer" },
        "response_verbose": { "type": "string" },
        "response_short": { "type": "string" },
        "search_terms": { "type": ["array"], "items": { "type": "string" } },
        "redirect": { "type": ["string","null"] },
        "photo_requested": { "type": "boolean" },
        "new_arrivals": { "type": "boolean" },
        "on_sale": { "type": "boolean" }
      },
      "required": [...]
    }
  }
}
```

#### Purpose & Use Case

**Trigger:** Never used in production, but designed as a replacement for the `/ai` endpoint.

**Function:** To get a structured JSON object directly from the LLM, validating the user's request against a massive, hardcoded list of product categories.

#### Architectural Comments & Project Utilization

**Assessment:** This is the direct predecessor to our architectural approach. The author correctly identified that relying on delimited strings was fragile and that native JSON output was the way forward. The inclusion of over 300 product categories directly in the prompt is inefficient and hard to maintain.

**Our Implementation:** This is fundamentally what our orchestrator will do, but with significant improvements:

- **Dynamic Category Awareness:** Instead of hardcoding categories in the prompt, we can use Retrieval-Augmented Generation (RAG). The orchestrator can perform a quick semantic search on the user's query against a vector index of our product categories. The top 5-10 matching categories can then be injected into the prompt at runtime. This makes the system dynamic and scalable.

- **Tool-Based Logic:** The boolean flags (`photo_requested`, `on_sale`, etc.) will be replaced by tool calls. For example, instead of a `photo_requested: true` flag, the LLM's response might include a request for the `request_photo_from_user` tool. This is a more robust and extensible agentic pattern.