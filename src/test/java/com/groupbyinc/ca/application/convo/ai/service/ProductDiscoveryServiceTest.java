package com.groupbyinc.ca.application.convo.ai.service;

import com.groupbyinc.ca.application.convo.ai.PromptManager;
import com.groupbyinc.ca.application.convo.ai.model.ModelConfig;
import com.groupbyinc.ca.application.convo.ai.tools.ProductDiscoveryTools;
import com.groupbyinc.ca.application.retail.search.SearchService;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for ProductDiscoveryService integration.
 * These tests verify that the service can be properly instantiated and configured.
 */
@MicronautTest
class ProductDiscoveryServiceTest {

    @Inject
    ProductDiscoveryTools productDiscoveryTools;

    @Inject
    PromptManager promptManager;

    @Inject
    SearchService searchService;

    @Test
    void testProductDiscoveryToolsInjection() {
        assertNotNull(productDiscoveryTools, "ProductDiscoveryTools should be injected");
    }

    @Test
    void testPromptManagerInjection() {
        assertNotNull(promptManager, "PromptManager should be injected");
    }

    @Test
    void testSearchServiceInjection() {
        assertNotNull(searchService, "SearchService should be injected");
    }

    @Test
    void testPromptTemplateLoading() {
        assertDoesNotThrow(() -> {
            var template = promptManager.getPromptTemplate("/prompts/product-discovery.md");
            assertNotNull(template, "Product discovery prompt template should be loaded");
        }, "Should be able to load product discovery prompt template");
    }

    @Test
    void testProductDiscoveryToolsSearchProduct() {
        // Test with empty query
        var emptyResult = productDiscoveryTools.searchProduct("");
        assertNotNull(emptyResult, "Should return empty map for empty query");
        assertTrue(emptyResult.isEmpty(), "Result should be empty for empty query");

        // Test with null query
        var nullResult = productDiscoveryTools.searchProduct(null);
        assertNotNull(nullResult, "Should return empty map for null query");
        assertTrue(nullResult.isEmpty(), "Result should be empty for null query");
    }

    @Test
    void testProductDiscoveryToolsSearchProducts() {
        // Test with empty list
        var emptyResult = productDiscoveryTools.searchProducts(java.util.List.of());
        assertNotNull(emptyResult, "Should return empty map for empty query list");
        assertTrue(emptyResult.isEmpty(), "Result should be empty for empty query list");

        // Test with null list
        var nullResult = productDiscoveryTools.searchProducts(null);
        assertNotNull(nullResult, "Should return empty map for null query list");
        assertTrue(nullResult.isEmpty(), "Result should be empty for null query list");
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "INTEGRATION_TEST", matches = "true")
    void testProductDiscoveryServiceCreation() {
        // This test would require actual LLM API keys and would be run only in integration environment
        // For now, we just verify that the components can be injected
        assertNotNull(productDiscoveryTools);
        assertNotNull(promptManager);
        assertNotNull(searchService);
    }
}
