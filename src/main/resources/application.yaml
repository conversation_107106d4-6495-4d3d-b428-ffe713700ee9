micronaut:
  application:
    name: conversational-ai
  server:
    port: 8080
    netty:
      worker:
        event-loop-group: httphandler
      server-type: full_content
  netty:
    event-loops:
      httphandler:
        num-threads: 10
        prefer-native-transport: true
      httpclient:
        num-threads: 10
        prefer-native-transport: true
  http:
    client:
      read-timeout: 10s
      max-content-length: 1MB
      event-loop-group: httpclient
    services:
      site-search:
        url: https://search.gbiqa-lo.groupbycloud.com
        pool:
          enabled: true
          max-connections: 20
        read-timeout: 5s
  cors:
    enabled: true
    configurations:
      all:
        allowedMethods:
          - GET
          - OPTIONS
          - POST
        exposedHeaders:
          - Authentication
          - Authorization
  security:
    enabled: true
    token:
      jwt:
        enabled: true
        signatures:
          jwks:
            groupby:
              url: http://auth-authentication-app-svc.authentication/.well-known/jwks.json
  caches:
    jwks:
      expire-after-write: 1h

endpoints:
  health:
    details-visible: ANONYMOUS
    discovery-client:
      enabled: false
    service-ready-indicator-enabled: false

resilience4j:
  thread-pool-bulkhead:
    enabled: true #Micronaut BulkheadInterceptor bean requires both bulkhead and thread-pool-bulkhead properties to be set
  ratelimiter:
    enabled: true
    instances:
      global:
        limit-for-period: 10
        limit-refresh-period: 1s
  bulkhead:
    enabled: true
    instances:
      global:
        maxConcurrentCalls: 5
gcp:
  project-id: gbiqa-lower-439d

mongodb:
  application-name: ${micronaut.application.name}-${gcp.project-id}
  uri: mongodb+srv://${mongo.account}:${mongo.key}@${mongo.host}
  read-preference: SECONDARY_PREFERRED
  ssl:
    enabled: true
  cluster:
    serverSelectionTimeout: 60000ms
  compressor-list:
    - snappy
    - zlib
    - zstd
  connection-pool:
    min-size: 2
    max-size: 10
    max-connecting: 2
    max-connection-idle-time: 60s

retail:
  search:
    key: ''
    customer: gbiqa
    collection: zapparel
    area: Production
    page-size: 20
    product-fields:
      - productId
      - title
      - description
      - price
      - originalPrice
    response-fields:
      - id
      - originalRequest
      - records
      - totalRecordCount
      - availableNavigation
      - pageInfo
      - availableNavigation
      - warnings

llm:
  models:
    orchestrator:
      provider: OPEN_AI # one of the dev.langchain4j.model.ModelProvider
      api-key: ${openai.api-key}
      model-name: gpt-4.1 # Latest OpenAi flagship model
      temperature: 0.1  # Lower for consistent routing decisions
      log-requests: true
      log-responses: true

    product-discovery:
      provider: GOOGLE_AI_GEMINI
      api-key: ${gemini.api-key}
      model-name: gemini-2.5-flash # Adaptive thinking, cost efficiency and speed
      temperature: 0.5
      log-requests: true
      log-responses: true

# mama mia
prompts:
  suggest: |
    Using the text submitted by the user, please predict the question they are asking as it pertains to an online housewares store search.
    Respond with 5 likely questions that fit, using provided response format.
  converse: |
    Please analyze the user's request. For any part that responds with search terms, include phrases like \"murphy beds\"
    if the user is looking to organize a bedroom or free up space in a bachelor apartment.
    If the user is asking for information about shipping or the store's return policy, respond with just a 0 followed by a colon,
    then followed by a friendly message in your own words that we do have information about that and to please wait a moment
    and they will be redirected to it, then follow that part by another colon and then followed by \"redirect\",
    then another colon, and then depending on which info they are asking for follow that colon by the word \"shipping\" or \"returns\".
    If it is asking for sale items or discounts or promo codes, but are not specific about the types of products they're looking for,
    then respond with just a 1 followed by a colon then followed by the search term \"on sale\" then followed by another colon,
    then followed by a brief, friendly message telling them that here are some of our top sale items.
    If it is asking for a list of items that match certain criteria and does not have a colon and if it hasn't already
    responded with a 1 and a colon and other stuff, then respond with just a 1.
    If the user is asking help to find a specific product or products that they list in the question without a colon,
    then respond with just a 2.
    If the user is clarifying by answering the assistant's question, then check the original question against the answer provided,
    or against the image if one is provided for context, and re-analyze the user's request.
    If the response is a 1 or a 2, and if it looks like they want to see items that are on sale, then add \"on sale\"
    to each of the search terms extracted for the response, but do not show \"on sale\" as a lone search term.
    If the response was just a 1 or just a 2, then check if the user specifically asked for items that go well with or
    match a room or something they have or even just has phrases similar to \"my home\" or \"my room\" or \"my apartment\" in the request.
    If that is the case, then place after the colon a comma-delimited list of relevant search terms
    (including context like \"for a specific room\" or in a specific color, etc) that might help them find what they're looking for,
    then another colon, and then a request to see if they are willing to share a photo of the room or items they are mentioning.
    If they were not asking for anything to go well with something else, and if the response was just a 1, then place a colon after the 1
    and follow that with just a comma-delimited list of search terms (including context like \"for a specific room\" or in a specific color, etc)
    that might help them find what they're looking for.
    After that comma-delimited list, place another colon, and following that, provide an answer to the user that briefly
    says the returned items will help satisfy their request, but do not repeat the list of search terms in that part of the response.
    Please follow this with a | character, followed by a similar but much more succinct/brief message.
    If the response was just a 2, and the user was not asking for anything to match something, then place a colon after the 2,
    and follow that with just a comma-delimited list of search terms (including context like \"for a specific room\" or in a specific color, etc)
    that might help them find what they're looking for.
    After that comma-delimited list, place another colon, and following that, provide an answer to the user that briefly says
    the returned items will help satisfy their request, but do not provide the list of search terms in that part of the response.
    Then place a | character, followed by a similar but more succinct message.
    If you have asked the user to share a photo for context, then add another colon to the response, followed by the word: photo.
    If the entire response appears to have a number, followed by a colon, followed by a comma-delimited list,followed by a colon,
    followed by another comma-delimited list, then please remove the 2nd comma-delimited list and the 2nd colon from the response.
    If at any point the user asked to see new arrivals, add the text \"~newArrivals\" to the end of the response.
    After checking all of this, if it is unclear what the user is asking, respond with a 0, followed by a colon, followed by a message indicating that you are unsure,
    and that they should stick to questions about Housewares products or general questions about the Housewares store.
