micronaut:
  application:
    name: conversational-ai
  server:
    port: 8080
    netty:
      worker:
        event-loop-group: httphandler
      server-type: full_content
  netty:
    event-loops:
      httphandler:
        num-threads: 10
        prefer-native-transport: true
      httpclient:
        num-threads: 10
        prefer-native-transport: true
  http:
    client:
      read-timeout: 10s
      max-content-length: 1MB
      event-loop-group: httpclient
    services:
      site-search:
        url: https://search.gbiqa-lo.groupbycloud.com
        pool:
          enabled: true
          max-connections: 20
        read-timeout: 5s
  cors:
    enabled: true
    configurations:
      all:
        allowedMethods:
          - GET
          - OPTIONS
          - POST
        exposedHeaders:
          - Authentication
          - Authorization
  security:
    enabled: true
    token:
      jwt:
        enabled: true
        signatures:
          jwks:
            groupby:
              url: http://auth-authentication-app-svc.authentication/.well-known/jwks.json
  caches:
    jwks:
      expire-after-write: 1h

endpoints:
  health:
    details-visible: ANONYMOUS
    discovery-client:
      enabled: false
    service-ready-indicator-enabled: false

resilience4j:
  thread-pool-bulkhead:
    enabled: true #Micronaut BulkheadInterceptor bean requires both bulkhead and thread-pool-bulkhead properties to be set
  ratelimiter:
    enabled: true
    instances:
      global:
        limit-for-period: 10
        limit-refresh-period: 1s
  bulkhead:
    enabled: true
    instances:
      global:
        maxConcurrentCalls: 5
gcp:
  project-id: gbiqa-lower-439d

mongodb:
  application-name: ${micronaut.application.name}-${gcp.project-id}
  uri: mongodb+srv://${mongo.account}:${mongo.key}@${mongo.host}
  read-preference: SECONDARY_PREFERRED
  ssl:
    enabled: true
  cluster:
    serverSelectionTimeout: 60000ms
  compressor-list:
    - snappy
    - zlib
    - zstd
  connection-pool:
    min-size: 2
    max-size: 10
    max-connecting: 2
    max-connection-idle-time: 60s

retail:
  search:
    key: ''
    customer: gbiqa
    collection: zapparel
    area: Production
    page-size: 20
    product-fields:
      - productId
      - title
      - description
      - price
      - originalPrice
    response-fields:
      - id
      - originalRequest
      - records
      - totalRecordCount
      - availableNavigation
      - pageInfo
      - availableNavigation
      - warnings

llm:
  models:
    router:
      provider: OPEN_AI # one of the dev.langchain4j.model.ModelProvider
      api-key: ${openai.api-key}
      model-name: gpt-4.1 # Latest OpenAi flagship model
      temperature: 0.1  # Lower for consistent routing decisions
      log-requests: true
      log-responses: true

    orchestrator:
      provider: OPEN_AI # one of the dev.langchain4j.model.ModelProvider
      api-key: ${openai.api-key}
      model-name: gpt-4.1 # Latest OpenAi flagship model
      temperature: 0.2
      log-requests: true
      log-responses: true

    product-discovery:
      provider: GOOGLE_AI_GEMINI
      api-key: ${gemini.api-key}
      model-name: gemini-2.5-flash # Adaptive thinking, cost efficiency and speed
      temperature: 0.5
      log-requests: true
