package com.groupbyinc.ca.api;

import com.groupbyinc.ca.api.model.ConverseRequest;
import com.groupbyinc.ca.api.model.ConverseResponse;
import com.groupbyinc.ca.api.model.SuggestRequest;
import com.groupbyinc.ca.api.model.SuggestResponse;
import com.groupbyinc.ca.api.security.Identity;
import com.groupbyinc.ca.application.convo.ai.service.OrchestrationService;

import io.github.resilience4j.micronaut.annotation.Bulkhead;
import io.github.resilience4j.micronaut.annotation.RateLimiter;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.TaskExecutors;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.micronaut.security.annotation.Secured;
import io.micronaut.security.rules.SecurityRule;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

@Slf4j
@RequiredArgsConstructor
@Controller("/api")
@Secured(SecurityRule.IS_AUTHENTICATED)
@ExecuteOn(TaskExecutors.BLOCKING)
@Bulkhead(name = "global")
@RateLimiter(name = "global")
public class ConversationalApi {
    private final ConversationService conversationService;

    @Post(value = "/converse/suggest", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public SuggestResponse suggest(@NotNull @Body @Valid SuggestRequest r, Identity identity) {
        setMDC(identity);
        log.info("New [suggest] request: {}", r);
        return conversationService.suggest(r);
    }

    @Post(value = "/converse", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public ConverseResponse converse(@NotNull @Body @Valid ConverseRequest r, Identity identity) {
        setMDC(identity);
        log.info("New [converse] request: {}", r);
        return conversationService.converse(r);
    }

    private void setMDC(Identity identity) {
        MDC.put("customer", identity.subject());
    }
}
