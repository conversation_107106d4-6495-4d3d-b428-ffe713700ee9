package com.groupbyinc.ca.api.model;

import io.micronaut.serde.annotation.Serdeable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

@Serdeable
public record ConverseRequest(
    @NotBlank String visitorId,
    String loginId,
    @NotBlank String sessionId,
    @NotEmpty @Valid List<Message> messages,
    @Valid Context context,
    Boolean alwaysReturnProducts
) {
    public ConverseRequest {
        if (alwaysReturnProducts == null) {
            alwaysReturnProducts = false;
        }
    }
}