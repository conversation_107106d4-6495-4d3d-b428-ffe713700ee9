package com.groupbyinc.ca.api.model;

import io.micronaut.serde.annotation.Serdeable;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
@Serdeable
public class UserContextDto {
    private String visitorId;
    private UserPreferencesDto preferences;
    private List<PurchaseDto> purchaseHistory;

    @Data
    @Serdeable
    public static class PurchaseDto {
        private String productId;
        private String productName;
        private double price;
        private Instant purchasedAt;
    }
}
