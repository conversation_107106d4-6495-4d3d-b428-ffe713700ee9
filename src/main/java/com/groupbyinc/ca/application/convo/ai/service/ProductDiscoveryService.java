package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

/**
 * AI service for extracting search queries from user messages and returning search results.
 * This service focuses on:
 * - Analyzing user messages to extract relevant search terms
 * - Using SearchService to find products
 * - Returning structured search results for the OrchestrationService to handle
 */
public interface ProductDiscoveryService {

    /**
     * Analyzes user message and returns search results.
     *
     * @param userMessage The user's message or query
     * @return SearchResult containing extracted queries and search results
     */
    @SystemMessage("""
        You are a product search query extractor. Your job is to analyze user messages and extract relevant search terms for product discovery.

        Extract 1-3 relevant search terms from the user's message that would help find products they're looking for.
        Focus on:
        - Product names, categories, or types
        - Key attributes (color, size, material, brand)
        - Use cases or purposes

        Return the search terms as a comma-separated list.
        If the message is unclear or not product-related, return an empty string.

        Examples:
        - "I need a warm jacket for winter" → "winter jacket, warm coat, outerwear"
        - "Looking for running shoes" → "running shoes, athletic footwear"
        - "Something for my living room" → "living room furniture, home decor"
        """)
    String extractSearchQueries(@UserMessage String userMessage);
}
