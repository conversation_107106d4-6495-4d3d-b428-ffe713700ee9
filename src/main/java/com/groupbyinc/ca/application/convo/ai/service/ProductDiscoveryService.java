package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.MemoryId;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

/**
 * AI service interface for product discovery and recommendation.
 * This service acts as an intelligent shopping assistant that can:
 * - Understand natural language product queries
 * - Search for relevant products using available tools
 * - Provide personalized recommendations
 * - Handle follow-up questions and refinements
 * - Maintain conversation context and memory
 */
public interface ProductDiscoveryService {

    /**
     * Main chat method for product discovery conversations.
     *
     * @param sessionId Unique identifier for the conversation session
     * @param userMessage The user's message or query
     * @param userContext JSON string containing user preferences, purchase history, and context
     * @return AI-generated response with product recommendations and conversational guidance
     */
    String chat(
        @MemoryId String sessionId,
        @UserMessage String userMessage,
        @V("user_context") String userContext
    );

    /**
     * Simplified chat method without user context for new or anonymous users.
     * 
     * @param sessionId Unique identifier for the conversation session
     * @param userMessage The user's message or query
     * @return AI-generated response with product recommendations
     */
    default String chat(@MemoryId String sessionId, @UserMessage String userMessage) {
        return chat(sessionId, userMessage, "{}");
    }
}
