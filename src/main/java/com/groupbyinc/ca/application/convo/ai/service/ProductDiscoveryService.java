package com.groupbyinc.ca.application.convo.ai.service;

import com.groupbyinc.ca.application.retail.search.SearchService;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Service that orchestrates product discovery by extracting search queries
 * from user messages and executing searches using SearchService.
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class ProductDiscoveryService {

    private final SearchQueryExtractor searchQueryExtractor;
    private final SearchService searchService;

    /**
     * Processes user message to extract search queries and execute product search.
     *
     * @param userMessage The user's message or query
     * @return SearchResult containing extracted queries and search results
     */
    public SearchResult discoverProducts(String userMessage) {
        log.info("Processing product discovery request: {}", userMessage);

        try {
            // Extract search queries using AI service
            List<String> extractedQueries = searchQueryExtractor.extractSearchQueries(userMessage);
            log.debug("Extracted queries: {}", extractedQueries);

            if (extractedQueries == null || extractedQueries.isEmpty()) {
                log.info("No search queries extracted from user message");
                return new SearchResult(List.of(), Map.of(), false);
            }

            log.info("Executing search with {} queries: {}", extractedQueries.size(), extractedQueries);

            // Execute search using SearchService
            Map<String, Map<String, Object>> searchResults = searchService.searchQueries(extractedQueries);

            log.info("Search completed. Found results for {} out of {} queries",
                    searchResults.size(), extractedQueries.size());

            boolean hasResults = !searchResults.isEmpty() &&
                searchResults.values().stream().anyMatch(result -> !result.isEmpty());

            return new SearchResult(extractedQueries, searchResults, hasResults);

        } catch (Exception e) {
            log.error("Error in product discovery for message '{}': {}", userMessage, e.getMessage(), e);
            return new SearchResult(List.of(), Map.of(), false);
        }
    }

    /**
     * Result object containing search queries and results.
     */
    public static class SearchResult {
        private final List<String> extractedQueries;
        private final Map<String, Map<String, Object>> searchResults;
        private final boolean hasResults;

        public SearchResult(List<String> extractedQueries,
                          Map<String, Map<String, Object>> searchResults,
                          boolean hasResults) {
            this.extractedQueries = extractedQueries;
            this.searchResults = searchResults;
            this.hasResults = hasResults;
        }

        public List<String> getExtractedQueries() {
            return extractedQueries;
        }

        public Map<String, Map<String, Object>> getSearchResults() {
            return searchResults;
        }

        public boolean hasResults() {
            return hasResults;
        }

        public boolean isEmpty() {
            return extractedQueries.isEmpty() && searchResults.isEmpty();
        }

        @Override
        public String toString() {
            return String.format("SearchResult{queries=%s, hasResults=%s, resultCount=%d}",
                extractedQueries, hasResults, searchResults.size());
        }
    }
}
