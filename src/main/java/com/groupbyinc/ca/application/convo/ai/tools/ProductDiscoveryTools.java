package com.groupbyinc.ca.application.convo.ai.tools;

import com.groupbyinc.ca.application.retail.search.SearchService;
import dev.langchain4j.agent.tool.Tool;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Tools for product discovery that wrap the SearchService functionality.
 * These tools are available to the ProductDiscoveryService AI agent to perform
 * product searches and retrieve relevant information.
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class ProductDiscoveryTools {

    private final SearchService searchService;

    /**
     * Search for products using one or more search queries.
     * This tool allows the AI to find products based on user descriptions, keywords, or specific requirements.
     * 
     * @param queries List of search terms or phrases to find relevant products (max 5 queries)
     * @return Map of search results where key is the query and value contains product information
     */
    @Tool("Search for products using natural language queries. Use this when users ask about specific products, categories, or describe what they're looking for.")
    public Map<String, Map<String, Object>> searchProducts(List<String> queries) {
        log.info("Searching for products with queries: {}", queries);
        
        if (queries == null || queries.isEmpty()) {
            log.warn("Empty or null queries provided to searchProducts tool");
            return Map.of();
        }
        
        try {
            Map<String, Map<String, Object>> results = searchService.searchQueries(queries);
            log.info("Search completed. Found results for {} out of {} queries", 
                    results.size(), queries.size());
            return results;
        } catch (Exception e) {
            log.error("Error occurred while searching for products: {}", e.getMessage(), e);
            return Map.of();
        }
    }

    /**
     * Search for a single product or category.
     * Convenience method for single query searches.
     * 
     * @param query Single search term or phrase
     * @return Search results for the query
     */
    @Tool("Search for products with a single query. Use this for focused searches on specific items or categories.")
    public Map<String, Object> searchProduct(String query) {
        log.info("Searching for product with query: {}", query);
        
        if (query == null || query.trim().isEmpty()) {
            log.warn("Empty or null query provided to searchProduct tool");
            return Map.of();
        }
        
        try {
            Map<String, Map<String, Object>> results = searchService.searchQueries(List.of(query.trim()));
            return results.getOrDefault(query.trim(), Map.of());
        } catch (Exception e) {
            log.error("Error occurred while searching for product: {}", e.getMessage(), e);
            return Map.of();
        }
    }

    /**
     * Search for products in multiple categories or with different variations.
     * Useful for exploring related products or alternatives.
     * 
     * @param primaryQuery Main search term
     * @param alternativeQueries Additional search terms for variations or alternatives
     * @return Combined search results from all queries
     */
    @Tool("Search for products with multiple related queries. Use this to explore variations, alternatives, or related categories.")
    public Map<String, Map<String, Object>> searchProductVariations(String primaryQuery, List<String> alternativeQueries) {
        log.info("Searching for product variations. Primary: {}, Alternatives: {}", primaryQuery, alternativeQueries);
        
        if (primaryQuery == null || primaryQuery.trim().isEmpty()) {
            log.warn("Empty primary query provided to searchProductVariations tool");
            return Map.of();
        }
        
        List<String> allQueries = new java.util.ArrayList<>();
        allQueries.add(primaryQuery.trim());
        
        if (alternativeQueries != null) {
            alternativeQueries.stream()
                .filter(q -> q != null && !q.trim().isEmpty())
                .map(String::trim)
                .forEach(allQueries::add);
        }
        
        return searchProducts(allQueries);
    }
}
