package com.groupbyinc.ca.application.convo.ai.service;

import com.groupbyinc.ca.api.model.*;
import com.groupbyinc.ca.application.convo.ConversationService;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.ca.application.convo.ai.service.IntentRouter.Route.PRODUCT_DISCOVERY;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class OrchestrationService implements ConversationService {
    private final IntentRouter router;
    private final ProductDiscoveryService productDiscoveryService;

    public String handleRequest(String sessionId, String userMessage, /*...*/) {
        // 1. Route the request to get a structured instruction
        IntentRouter.RouteInstruction instruction = router.route(userMessage, sessionId);

        // 2. Delegate to the appropriate specialist agent
        return switch (instruction.route()) {
            case PRODUCT_DISCOVERY -> productDiscoveryService.chat(sessionId, userMessage, /*...*/);
            case UNKNOWN ->
            // case CART_MANAGEMENT -> cartAgent.chat(...);
            // case CUSTOMER_SERVICE -> customerServiceAgent.chat(...);
            default -> "I'm sorry, I'm not sure how to help with that. Could you please rephrase?";
        };
    }

    @Override
    public SuggestResponse suggest(SuggestRequest suggestRequest) {
        return null;
    }

    @Override
    public ConverseResponse converse(ConverseRequest converseRequest) {
        return null;
    }
}
}
