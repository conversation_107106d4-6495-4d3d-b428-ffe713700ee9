package com.groupbyinc.ca.application.convo.ai.service;

import static com.groupbyinc.ca.application.convo.ai.service.IntentRouter.Route.PRODUCT_DISCOVERY;

public class OrchestrationService {
    private final IntentRouter router;
    private final ProductDiscoveryAgent productDiscoveryAgent;
    // ... other agents

    public OrchestrationService(IntentRouter router, ProductDiscoveryAgent productDiscoveryAgent) {
        this.router = router;
        this.productDiscoveryAgent = productDiscoveryAgent;
    }

    public String handleRequest(String sessionId, String userMessage, /*...*/) {
        // 1. Route the request to get a structured instruction
        IntentRouter.RouteInstruction instruction = router.route(userMessage, sessionId);

        // 2. Delegate to the appropriate specialist agent
        return switch (instruction.route()) {
            case PRODUCT_DISCOVERY -> productDiscoveryAgent.chat(sessionId, userMessage, /*...*/);
            // case CART_MANAGEMENT -> cartAgent.chat(...);
            // case CUSTOMER_SERVICE -> customerServiceAgent.chat(...);
            default -> "I'm sorry, I'm not sure how to help with that. Could you please rephrase?";
        };
    }
}
}
