package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.AiServices;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;

@Factory
public class ServiceFactory {

    @Bean
    @Context
    public IntentRouter intentRouter() {
        return AiServices.create(IntentRouter.class, model);
    }


}
