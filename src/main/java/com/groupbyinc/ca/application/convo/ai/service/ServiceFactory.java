package com.groupbyinc.ca.application.convo.ai.service;

import com.groupbyinc.ca.application.convo.ai.model.ModelConfig;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.AiServices;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import jakarta.inject.Named;

import static dev.langchain4j.model.chat.Capability.RESPONSE_FORMAT_JSON_SCHEMA;

@Factory
public class ServiceFactory {

    @Bean
    @Context
    public IntentRouter intentRouter(@Named("orchestrator") ModelConfig modelConfig) {
        return AiServices.create(IntentRouter.class, createModel(modelConfig));
    }

    @Bean
    @Context
    public SearchQueryExtractor searchQueryExtractor(@Named("product-discovery") ModelConfig modelConfig) {
        return AiServices.create(SearchQueryExtractor.class, createModel(modelConfig));
    }

    @Bean
    @Context
    public SuggestService suggestService(@Named("orchestrator") ModelConfig modelConfig) {
        return AiServices.create(SuggestService.class, createModel(modelConfig));
    }


    private ChatModel createModel(ModelConfig config) {
        return switch (config.getProvider()) {
            case OPEN_AI -> OpenAiChatModel.builder()
                .apiKey(config.getApiKey())
                .modelName(config.getModelName())
                .temperature(config.getTemperature())
                .supportedCapabilities(RESPONSE_FORMAT_JSON_SCHEMA)
                .strictJsonSchema(true)
                .logRequests(config.getLogRequests())
                .logResponses(config.getLogRequests())
                .build();

            case GOOGLE_AI_GEMINI -> GoogleAiGeminiChatModel.builder()
                .apiKey(config.getApiKey())
                .modelName(config.getModelName())
                .temperature(config.getTemperature())
                .supportedCapabilities(RESPONSE_FORMAT_JSON_SCHEMA)
                .logRequestsAndResponses(config.getLogRequests())
                .build();

            default -> throw new IllegalArgumentException("Unknown provider: " + config.getProvider());
        };
    }

}
