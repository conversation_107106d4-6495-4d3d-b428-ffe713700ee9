package com.groupbyinc.ca.application.convo;

import com.groupbyinc.ca.api.model.ConverseRequest;
import com.groupbyinc.ca.api.model.ConverseResponse;
import com.groupbyinc.ca.api.model.SuggestRequest;
import com.groupbyinc.ca.api.model.SuggestResponse;
import com.groupbyinc.ca.application.metrics.LogExecutionTime;

public interface ConversationService {
    @LogExecutionTime
    SuggestResponse suggest(SuggestRequest suggestRequest);

    @LogExecutionTime
    ConverseResponse converse(ConverseRequest converseRequest);
}
