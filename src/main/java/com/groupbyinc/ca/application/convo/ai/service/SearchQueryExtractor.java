package com.groupbyinc.ca.application.convo.ai.service;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;

import java.util.List;

/**
 * AI service for extracting search queries from user messages.
 * This service focuses on analyzing user messages to extract relevant search terms
 * that can be used with SearchService.searchQueries() method.
 */
public interface SearchQueryExtractor {

    /**
     * Analyzes user message and extracts up to 5 search queries.
     * 
     * @param userMessage The user's message or query
     * @return List of search terms (up to 5) extracted from the user message
     */
    @SystemMessage("""
        You are a product search query extractor. Your job is to analyze user messages and extract relevant search terms for product discovery.
        
        Extract up to 5 relevant search terms from the user's message that would help find products they're looking for.
        Focus on:
        - Product names, categories, or types
        - Key attributes (color, size, material, brand)
        - Use cases or purposes
        - Variations and alternatives
        
        Return ONLY a JSON array of strings containing the search terms.
        If the message is unclear or not product-related, return an empty array.
        
        Examples:
        - "I need a warm jacket for winter" → ["winter jacket", "warm coat", "outerwear", "winter clothing"]
        - "Looking for running shoes" → ["running shoes", "athletic footwear", "sneakers"]
        - "Something for my living room" → ["living room furniture", "home decor", "furniture"]
        """)
    List<String> extractSearchQueries(@UserMessage String userMessage);
}
