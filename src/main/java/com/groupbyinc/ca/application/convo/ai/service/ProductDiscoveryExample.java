package com.groupbyinc.ca.application.convo.ai.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Example class demonstrating how to use the ProductDiscoveryService.
 * This shows the integration pattern for using the AI service with user context.
 */
@Slf4j
@RequiredArgsConstructor
public class ProductDiscoveryExample {

    private final ProductDiscoveryService productDiscoveryService;
    private final ObjectMapper objectMapper;

    /**
     * Example of a simple product discovery conversation without user context.
     */
    public String simpleProductDiscovery(String sessionId, String userMessage) {
        log.info("Starting simple product discovery for session: {}", sessionId);
        
        try {
            String response = productDiscoveryService.chat(sessionId, userMessage);
            log.info("Product discovery response generated for session: {}", sessionId);
            return response;
        } catch (Exception e) {
            log.error("Error in product discovery for session {}: {}", sessionId, e.getMessage(), e);
            return "I'm sorry, I'm having trouble processing your request right now. Please try again.";
        }
    }

    /**
     * Example of product discovery with user context for personalization.
     */
    public String personalizedProductDiscovery(String sessionId, String userMessage, UserContext userContext) {
        log.info("Starting personalized product discovery for session: {}", sessionId);
        
        try {
            String userContextJson = serializeUserContext(userContext);
            String response = productDiscoveryService.chat(sessionId, userMessage, userContextJson);
            log.info("Personalized product discovery response generated for session: {}", sessionId);
            return response;
        } catch (Exception e) {
            log.error("Error in personalized product discovery for session {}: {}", sessionId, e.getMessage(), e);
            return "I'm sorry, I'm having trouble processing your request right now. Please try again.";
        }
    }

    /**
     * Example of handling a conversation flow with multiple turns.
     */
    public String continueConversation(String sessionId, String userMessage, UserContext userContext) {
        log.info("Continuing conversation for session: {}", sessionId);
        
        try {
            String userContextJson = serializeUserContext(userContext);
            
            // The ProductDiscoveryService maintains conversation memory automatically
            // Each call with the same sessionId continues the previous conversation
            String response = productDiscoveryService.chat(sessionId, userMessage, userContextJson);
            
            log.info("Conversation continued for session: {}", sessionId);
            return response;
        } catch (Exception e) {
            log.error("Error continuing conversation for session {}: {}", sessionId, e.getMessage(), e);
            return "I'm sorry, I'm having trouble processing your request right now. Please try again.";
        }
    }

    /**
     * Serialize user context to JSON string for the AI service.
     */
    private String serializeUserContext(UserContext userContext) {
        if (userContext == null) {
            return "{}";
        }
        
        try {
            return objectMapper.writeValueAsString(userContext);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize user context: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * Example user context class for demonstration.
     */
    public static class UserContext {
        private String name;
        private String email;
        private java.util.List<String> preferences;
        private java.util.List<PurchaseHistory> purchaseHistory;

        // Constructors, getters, and setters
        public UserContext() {}

        public UserContext(String name, String email) {
            this.name = name;
            this.email = email;
            this.preferences = new java.util.ArrayList<>();
            this.purchaseHistory = new java.util.ArrayList<>();
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public java.util.List<String> getPreferences() { return preferences; }
        public void setPreferences(java.util.List<String> preferences) { this.preferences = preferences; }

        public java.util.List<PurchaseHistory> getPurchaseHistory() { return purchaseHistory; }
        public void setPurchaseHistory(java.util.List<PurchaseHistory> purchaseHistory) { this.purchaseHistory = purchaseHistory; }
    }

    /**
     * Example purchase history class for demonstration.
     */
    public static class PurchaseHistory {
        private String productId;
        private String productName;
        private String category;
        private double price;
        private java.time.Instant purchaseDate;

        // Constructors, getters, and setters
        public PurchaseHistory() {}

        public PurchaseHistory(String productId, String productName, String category, double price) {
            this.productId = productId;
            this.productName = productName;
            this.category = category;
            this.price = price;
            this.purchaseDate = java.time.Instant.now();
        }

        public String getProductId() { return productId; }
        public void setProductId(String productId) { this.productId = productId; }

        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }

        public java.time.Instant getPurchaseDate() { return purchaseDate; }
        public void setPurchaseDate(java.time.Instant purchaseDate) { this.purchaseDate = purchaseDate; }
    }
}
