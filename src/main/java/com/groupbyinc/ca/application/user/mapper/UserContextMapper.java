package com.groupbyinc.ca.application.user.mapper;

import com.groupbyinc.ca.api.model.UserContextDto;
import com.groupbyinc.ca.api.model.UserContextDto.UserPreferencesDto;
import com.groupbyinc.ca.application.user.entity.UserContext;

import java.util.List;
import java.util.stream.Collectors;

public class UserContextMapper {

    public static UserContextDto toDto(UserContext entity) {
        if (entity == null) return null;
        var dto = new UserContextDto();
        dto.setVisitorId(entity.getVisitorId());
        dto.setPreferences(toPreferencesDto(entity.getPreferences()));
        dto.setPurchaseHistory(toPurchaseDto(entity.getPurchaseHistory()));
        return dto;
    }

    public static UserContextDto.UserPreferencesDto toPreferencesDto(UserContext.UserPreferences entity) {
        if (entity == null) return null;
        var dto = new UserContextDto.UserPreferencesDto();
        dto.setName(entity.getName());
        dto.setSurname(entity.getSurname());
        dto.setTitle(entity.getTitle());
        dto.setMetadata(entity.getMetadata());
        return dto;
    }

    public static List<UserContextDto.PurchaseDto> toPurchaseDto(List<UserContext.Purchase> entities) {
        if (entities == null) return null;
        return entities.stream().map(UserContextMapper::toPurchaseDto).collect(Collectors.toList());
    }

    public static UserContextDto.PurchaseDto toPurchaseDto(UserContext.Purchase entity) {
        if (entity == null) return null;
        var dto = new UserContextDto.PurchaseDto();
        dto.setProductId(entity.getProductId());
        dto.setProductName(entity.getProductName());
        dto.setPrice(entity.getPrice());
        dto.setPurchasedAt(entity.getPurchasedAt());
        return dto;
    }

    public static UserContext toEntity(UserContextDto dto) {
        if (dto == null) return null;
        var entity = new UserContext();
        entity.setVisitorId(dto.getVisitorId());
        entity.setPreferences(toPreferencesEntity(dto.getPreferences()));
        entity.setPurchaseHistory(toPurchaseEntity(dto.getPurchaseHistory()));
        return entity;
    }


    public static UserContext.UserPreferences toPreferencesEntity(UserContextDto.UserPreferencesDto dto) {
        if (dto == null) return null;
        var entity = new UserContext.UserPreferences();
        entity.setName(dto.getName());
        entity.setSurname(dto.getSurname());
        entity.setTitle(dto.getTitle());
        entity.setMetadata(dto.getMetadata());
        return entity;
    }

    public static List<UserContext.Purchase> toPurchaseEntity(List<UserContextDto.PurchaseDto> dtos) {
        if (dtos == null) return null;
        return dtos.stream().map(UserContextMapper::toPurchaseEntity).collect(Collectors.toList());
    }


    public static UserContext.Purchase toPurchaseEntity(UserContextDto.PurchaseDto dto) {
        if (dto == null) return null;
        var entity = new UserContext.Purchase();
        entity.setProductId(dto.getProductId());
        entity.setProductName(dto.getProductName());
        entity.setPrice(dto.getPrice());
        entity.setPurchasedAt(dto.getPurchasedAt());
        return entity;
    }

}

