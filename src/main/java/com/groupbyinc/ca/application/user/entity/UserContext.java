package com.groupbyinc.ca.application.user.entity;

import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import io.micronaut.serde.annotation.Serdeable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.bson.types.ObjectId;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import static com.groupbyinc.ca.application.migration.MongoMigration.C_USER_CONTEXT;

@Data
@MappedEntity(C_USER_CONTEXT)
public class UserContext {
    @Id
    @GeneratedValue
    private ObjectId id;
    @NotBlank
    private String visitorId;
    private UserPreferences preferences;
    private List<Purchase> purchaseHistory;

    @Data
    @Serdeable
    public static class Purchase {
        private String productId;
        private String productName;
        private double price;
        private Instant purchasedAt;
    }

    @Data
    @Serdeable
    public static class UserPreferences {
        private String name;
        private String surname;
        private String title;
        private Map<String, Object> metadata;
    }
}
