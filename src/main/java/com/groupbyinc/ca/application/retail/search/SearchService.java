package com.groupbyinc.ca.application.retail.search;

import com.groupbyinc.ca.application.metrics.LogExecutionTime;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Context
public class SearchService {
    private static final ExecutorService EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();

    private final SearchClient searchClient;
    private final String collection;
    private final String area;
    private final int pageSize;
    private final List<String> productFields;
    private final List<String> responseFields;


    public SearchService(SearchClient searchClient,
                         @Value("${retail.search.collection}") String collection,
                         @Value("${retail.search.area}") String area,
                         @Value("${retail.search.page-size}") int pageSize,
                         @Value("${retail.search.product-fields}") List<String> productFields,
                         @Value("${retail.search.response-fields}") List<String> responseFields) {
        this.searchClient = searchClient;
        this.collection = collection;
        this.area = area;
        this.pageSize = pageSize;
        this.productFields = productFields;
        this.responseFields = responseFields;
    }

    @LogExecutionTime
    public Map<String, Map<String, Object>> searchQueries(List<String> queries) {
        if (CollectionUtils.isEmpty(queries)) {
            return Map.of();
        }
        if (queries.size() > 5) {
            queries = queries.subList(0, 5);
        }
        var results = new ConcurrentHashMap<String, Map<String, Object>>();
        var futures = queries.stream()
            .map(q -> CompletableFuture.runAsync(() -> search(q).ifPresent(m -> results.put(q, m)), EXECUTOR))
            .toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return results;
    }

    private Optional<Map<String, Object>> search(String query) {
        try {
            var req = new SearchClient.Request(query, collection, area, productFields, pageSize, 0);
            var resp = searchClient.search(req);
            resp.keySet().removeIf(s -> !responseFields.contains(s));
            return Optional.of(resp);

        } catch (Exception e) {
            log.error("Failed to search [{}]: {}", query, e.getMessage(), e);
            return Optional.empty();
        }
    }
}
