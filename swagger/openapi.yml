openapi: 3.1.0
info:
  title: Conversational Commerce Engine API
  description: API for the AI-driven Conversational Commerce Engine. It facilitates the interaction between the UI client and the Java Orchestrator backend.
  version: 1.0.0
servers:
  - url: /api/v1
tags:
  - name: Conversation
    description: Endpoints for managing conversational interactions.

paths:
  /converse:
    post:
      tags:
        - Conversation
      summary: Main conversation endpoint
      description: Sends the user's message and context to the orchestrator and receives a comprehensive payload for UI rendering.
      operationId: postConverse
      requestBody:
        description: The user's input and session context.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConverseRequest'
      responses:
        '200':
          description: Successful response with all data needed to render the next conversational turn.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConverseResponse'
        '400':
          description: Bad Request. The request is malformed.
        '500':
          description: Internal Server Error.

  /converse/suggest:
    post:
      tags:
        - Conversation
      summary: Get conversational suggestions (SAYT)
      description: Provides predictive search suggestions as a user types a query.
      operationId: postConverseSuggest
      requestBody:
        description: The partial query and session context.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SuggestRequest'
      responses:
        '200':
          description: A list of query suggestions.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestResponse'
        '400':
          description: Bad Request. The request is malformed.
        '500':
          description: Internal Server Error.

  /image:
    post:
      tags:
        - Conversation
      summary: Upload an image for context
      description: Handles the upload of a single image file and returns a unique identifier for use in the main /converse endpoint.
      operationId: postImage
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                visitorId:
                  type: string
                  description: The unique identifier for the user.
                sessionId:
                  type: string
                  description: The unique identifier for the current conversation session.
                image:
                  type: string
                  format: binary
                  description: The image file to upload.
              required:
                - visitorId
                - sessionId
                - image
      responses:
        '200':
          description: Successful upload, returning a unique image ID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageUploadResponse'
        '400':
          description: Bad Request. Malformed request or invalid file type.
        '413':
          description: Payload Too Large. The image exceeds the 10MB limit.
        '500':
          description: Internal Server Error.

components:
  schemas:
    # Main Converse Endpoint Schemas
    ConverseRequest:
      type: object
      required:
        - visitorId
        - sessionId
        - messages
      properties:
        visitorId:
          type: string
          description: A unique, persistent identifier for the user.
          example: user-xyz-789
        loginId:
          type: string
          description: The login identifier for the user (e.g., email).
          example: "<EMAIL>"
        sessionId:
          type: string
          description: A unique identifier for the current conversation session.
          example: session-abc-123
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        context:
          $ref: '#/components/schemas/Context'
        alwaysReturnProducts:
          type: boolean
          default: false
          description: If true, the server will attempt to return products even for non-product-list responses.

    ConverseResponse:
      type: object
      required:
        - sessionId
        - responseType
        - assistantMessage
        - ui
        - data
      properties:
        sessionId:
          type: string
          example: session-abc-123
        responseType:
          $ref: '#/components/schemas/ResponseType'
        assistantMessage:
          $ref: '#/components/schemas/AssistantMessage'
        ui:
          $ref: '#/components/schemas/UI'
        data:
          $ref: '#/components/schemas/ResponseData'

    # Suggest Endpoint Schemas
    SuggestRequest:
      type: object
      required:
        - visitorId
        - sessionId
        - query
      properties:
        visitorId:
          type: string
          example: user-xyz-789
        sessionId:
          type: string
          example: session-abc-123
        query:
          type: string
          example: "throw pill"

    SuggestResponse:
      type: object
      required:
        - suggestions
      properties:
        suggestions:
          type: array
          items:
            type: string
          example: ["throw pillows on sale", "throw pillows modern style"]

    # Image Upload Schemas
    ImageUploadResponse:
      type: object
      required:
        - imageId
      properties:
        imageId:
          type: string
          description: A unique, server-generated identifier for the uploaded image.
          example: image-ref-def-456

    # Reusable Data Models
    Message:
      type: object
      required:
        - type
        - content
      properties:
        type:
          $ref: '#/components/schemas/MessageType'
        content:
          type: string
          description: Text content or an imageId.
          example: "Show me more like this."

    Context:
      type: object
      properties:
        productIds:
          type: array
          items:
            type: string
          description: The IDs of the products the user is currently viewing.
          example: ["prod-111", "prod-222"]
        userContext:
          $ref: '#/components/schemas/UserContext'

    UserContext:
      type: object
      properties:
        name:
          type: string
          example: "Pavel"
        surname:
          type: string
          example: "Shakhlovich"
        title:
          type: string
          example: "Your majesty"
        metadata:
          type: object
          additionalProperties: true
          example:
            gender: "male"
            age: 44

    AssistantMessage:
      type: object
      properties:
        verbose:
          type: string
          example: "Of course! Here are a few styles you might like."
        short:
          type: string
          example: "Here are some popular styles."

    UI:
      type: object
      properties:
        actions:
          type: array
          items:
            $ref: '#/components/schemas/Action'

    Action:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ActionType'
        prompt:
          type: string
          example: "Are you looking for a specific pattern?"
        options:
          type: array
          items:
            type: string
          example: ["Earrings", "Necklaces"]
        data:
          # This field can contain any structure, depending on the action type.
          # For CART_ACTION, it would contain cart update details.
          type: object
          additionalProperties: true

    ResponseData:
      type: object
      properties:
        search:
          type: object
          description: Raw search response from the retail search API
          additionalProperties: true
          example:
            id: "ce870f66-4258-46ea-9965-67b1fb0b8e17"
            area: "Production"
            query: "dress"
            records:
              - $ref: '#/components/schemas/S4R_Record'
            totalRecordCount: 638
            pageInfo:
              recordStart: 1
              recordEnd: 10
            availableNavigation: []
            selectedNavigation: []
        redirectUrl:
          type: string
          format: uri
          example: "/returns-policy"

    # Schema for the S4R Record
    S4R_Record:
      type: object
      properties:
        _id:
          type: string
        _u:
          type: string
          format: uri
        _t:
          type: string
        collection:
          type: string
        allMeta:
          $ref: '#/components/schemas/AllMeta'

    AllMeta:
      type: object
      properties:
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageObject'
        brands:
          type: array
          items:
            type: string
        name:
          type: string
        description:
          type: string
        attributes:
          $ref: '#/components/schemas/AttributesObject'
        variants:
          type: array
          items:
            $ref: '#/components/schemas/Variant'
        id:
          type: string
        availability:
          type: string
        title:
          type: string
        primaryProductId:
          type: string

    Variant:
      type: object
      properties:
        name:
          type: string
        id:
          type: string
        type:
          type: string
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageObject'
        brands:
          type: array
          items:
            type: string
        patterns:
          type: array
          items:
            type: string
        description:
          type: string
        availability:
          type: string
        title:
          type: string
        primaryProductId:
          type: string
        priceInfo:
          $ref: '#/components/schemas/PriceInfo'
        sizes:
          type: array
          items:
            type: string
        materials:
          type: array
          items:
            type: string
        attributes:
          $ref: '#/components/schemas/AttributesObject'

    ImageObject:
      type: object
      properties:
        uri:
          type: string
          format: uri

    PriceInfo:
      type: object
      properties:
        price:
          type: number
          format: float

    AttributesObject:
      type: object
      additionalProperties:
        type: object
        properties:
          text:
            type: array
            items:
              type: string

    # Enums
    MessageType:
      type: string
      enum: [TEXT, IMAGE]
    ResponseType:
      type: string
      enum: [ANSWER, PRODUCT_LIST, REDIRECT, ERROR]
    ActionType:
      type: string
      enum: [CLARIFICATION, REFINEMENT_OPTIONS, CART_ACTION]
